import { CONTENT_TYPE_MAP } from '@/const/common';
import { LoginStateAtom } from '@/model/login';
import { ORIGIN_TYPE } from '@/pages/Editor/common/openEditor';
import { PlayConfig, Script } from '@/type/pagedoo';
import {
  LivePlayPull,
  liveBackgroundMusic,
  liveBkg,
  liveImage,
  liveSpeech,
  liveVideo,
  midasbuyEvent,
  virtualmanTXCZ,
} from '@/utils/play-component';
import { allViews } from '@/utils/play-view';
import { ScriptPlugin, SinglePlugin } from '@/utils/template/index';
import { getParam, uuid } from '@tencent/midas-util';
import { PagedooPlayConfig } from '@tencent/pagedoo-time-navigator/es/type';
import { getRecoil } from 'recoil-nexus';
// import { initPPTTemplate } from '@/utils/template/ppt'; // PPT模板
import { BaseScript } from '@/components/ScriptForm/type';
import { getParams } from '@/utils/url';
import { CommonStyle } from '@tencent/pagedoo-library';
import { cloneDeep } from 'lodash-es';
import { LIVE_TEMPLATES, VIDEO_TEMPLATES } from './templates';
import {
  CLIPPING_SHAPE_KEYWORD,
  CURRENT_LANGUAGE,
  LANGUAGE_SPEED,
  VIRTUALMAN,
  VOICE_ID_MAP,
  useTencentCloudTTS,
} from './txcz/constants';
import { scriptData } from './txcz/txcz';
import { TXCZ_TEMPLATE_COMPONENTS } from './txcz/type';
import {
  isCommentMode,
  isFullScreenMode,
  isVirtualmanInClippingRegion,
} from './txcz/utils';
import { Template } from './type';

export const generateTemplateId = () => {
  return `${uuid()}`;
};

// console.log('生成模板ID', generateTemplateId());

// 格式化 清除无用节点
export const format = (conf: PlayConfig): PlayConfig => {
  for (const timeline of conf.timeline) {
    timeline.node = timeline.node.filter(Boolean);
  }
  conf.timeline = conf.timeline.filter((i) => i.node.length > 0);
  conf.__config.scaleX = 80;
  return conf;
};

// 设置meta信息
// - size
export const setMeta =
  ({ size }: { size: [number, number] }) =>
  (conf: PlayConfig) => {
    conf.meta = {
      ...(conf.meta ?? {}),
      size,
    };
  };

// 初始化时间轴
export const initPlugin = (
  script: BaseScript,
  length = 5
  // extra?: number[]
): SinglePlugin => {
  return (conf: PlayConfig) => {
    // 背景图
    // 数字人后景
    // 桌子
    // 商品图
    // 数字人前景
    for (let i = 0; i < length; i++) {
      conf.timeline[i] = {
        __config: { height: 40 },
        node: new Array(script.views.length).fill(null),
      };
    }
    // if (extra?.length) {
    //   extra.forEach((index) => {
    //     conf.timeline[index] = {
    //       __config: { height: 40 },
    //       node: new Array(script.views.length).fill(null),
    //     };
    //   });
    // }
  };
};

export const duration = (defaultDuration = 10000): SinglePlugin => {
  return (conf: PlayConfig) => {
    // timeline最大节点数量
    const max = Math.max(...conf.timeline.map((i) => i.node.length));
    let offset = 0;
    for (let i = 0; i < max; i++) {
      const speechWords: string | undefined = conf.timeline
        .map((timeline) => timeline.node[i])
        .filter(Boolean)
        .find(
          (i) =>
            i.component.id.includes('LiveSpeech') &&
            i.component.data.speechData.text
        )?.component.data.speechData.text;
      // 计算“列”的时长
      const duration = Math.max(
        speechWords ? speechWords.length * LANGUAGE_SPEED : defaultDuration, // 有数字人的情况按口播文案估算时长
        1000 // 无数字人的情况按1000估算时长（即10秒）
      );
      // 修改timeline这一列上所有Node的duration和offset
      let nodeDuration = 0;
      for (const pagedooPlayTimeline of conf.timeline) {
        const node = pagedooPlayTimeline.node[i];
        if (!node) continue;
        nodeDuration =
          node.duration < 0
            ? duration + node.duration
            : node.duration > 0
            ? node.duration
            : duration;
        node.offset += offset;
        node.duration = nodeDuration;
      }

      // 打环节标
      conf.fragment.push({
        __config: { name: '' },
        id: uuid(),
        offset,
      });
      offset += duration;
    }
  };
};

// 计算节点时长
export const durationWithNumber =
  (options: { duration?: number }): SinglePlugin =>
  (conf) => {
    const max = Math.max(...conf.timeline.map((i) => i.node.length));
    let offset = 0;
    for (let i = 0; i < max; i++) {
      const duration = options?.duration || 20000;
      for (const pagedooPlayTimeline of conf.timeline) {
        const node = pagedooPlayTimeline.node[i];
        if (!node) continue;
        node.duration = duration;
        node.offset = offset;
      }
      conf.fragment.push({
        __config: { name: '' },
        id: uuid(),
        offset,
      });
      offset += duration;
    }
  };
// 数字人需要liveid，未设置liveid会导致播放出错
export const livePlugin = (conf: PlayConfig) => {
  const loginState = getRecoil(LoginStateAtom);
  const liveID = loginState?.openid || 'live';
  for (const timeline of conf.timeline) {
    for (const node of timeline.node) {
      if (!node) continue;
      if (node.component.id.includes('Virtualman')) {
        node.component.data.liveID = liveID;
      }
    }
  }
};

// 腾讯充值直播间相关
// 添加背景图
const scriptTXCZBkgPlugin = (
  script: Script,
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (view?.backgroundImage[0]?.url) {
        conf.timeline[timelineKey].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveBkg(
            key,
            view.backgroundImage[0]?.url || '', // 背景图
            '', // prompt
            allViews['腾讯充值'].bkg
          ),
          duration: 0,
          id: uuid(),
          key,
          offset: 0,
        };
      }
    }
  };
};

// BGM
const scriptAdBackgroundMusicPlugin = (
  script: Script,
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PlayConfig) => {
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      const fullScreen = isFullScreenMode(view?.台词文案);
      const isPlayingGame = isCommentMode(view?.台词文案) && view.video;
      conf.timeline[timelineKey].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration: 0,
        component: liveBackgroundMusic(
          key,
          fullScreen || isPlayingGame ? 1 : 10,
          [
            {
              id: uuid(),
              title: 'springlive',
              url: `https://pagedoo-sandbox-cn-1259340503.cos.ap-hongkong.myqcloud.com/uploads/live/others/1.mp3`,
              duration: '',
            },
          ],
          true,
          allViews['腾讯充值'].hidden
        ),
        duration: 0,
        id: uuid(),
        key,
        offset: 0,
      };
    }
  };
};

function getVideoDuration(url: string): Promise<number> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');

    video.addEventListener('loadedmetadata', () => {
      const { duration } = video;
      resolve(duration * 1000);

      // Clean up
      video.remove();
    });

    video.addEventListener('error', (error) => {
      reject(error);

      // Clean up
      video.remove();
    });

    video.src = url;
    video.style.display = 'none';
    document.body.appendChild(video);
  });
}

// 添加视频
const scriptTXCZVideoPlugin = async (
  script: Script,
  timelineKey: number,
  key: number
): Promise<SinglePlugin> => {
  return async (conf: PagedooPlayConfig) => {
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (view?.video) {
        if (view.video[0]?.url) {
          const positionKey = isFullScreenMode(view?.台词文案)
            ? 'phone_in_fullscreen'
            : (view.video[0].orientation as string);
          const _duration = await getVideoDuration(view.video[0]?.url).catch(
            () => 0
          );
          conf.timeline[timelineKey].node[index] = {
            __config: { thumbnail: '', title: '', type: 'component' },
            actualDuration: 0,
            component: liveVideo(
              key,
              view.video[0]?.url || '', // 视频URL
              allViews['腾讯充值'][view.video[0]?.type || positionKey] ||
                allViews['腾讯充值'][positionKey] ||
                {},
              0,
              false
            ),
            duration: _duration - videoNodeOffsetTime,
            id: uuid(),
            key,
            offset: videoNodeOffsetTime,
          };
        }
      }
    }
  };
};
// 主播打游戏
const scriptTXCZPlayPullPlugin = (
  script: Script,
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (view?.video) {
        if (view.video[0]?.url === '' && view.video[0]?.type === 'live') {
          conf.timeline[timelineKey].node[index] = {
            __config: { thumbnail: '', title: '', type: 'component' },
            actualDuration: 0,
            component: LivePlayPull(
              key,
              'https://csycdn.flv.wxqcloud.qq.com/trtc_1400419933/111583_136b1b36e9798f568501263dc4a775d2.flv?extbuf=uONV8K0iSzxVraFHuTqAo%2FeA938iEOnfe9zpH6bgwQb9bkQLwaVFMVQ5FMFgy%2FqBw3hAhYnzFq%2BEkWVoe1Q3vRwbR2QAIf0M5p3cFFVl3jtO8%2Bq%2F3LeSzKT%2B9veFC0YceyO1W7tYRNf4Bgbj78qb6dDCyRG3&openid=d80d9823b28181c3b7aeb539dac1a0ad&txSecret=975935d94f520656adacb4029e5beb7e&txTime=66A5BD54&wxtoken=0bdfb5eb623a2d8a7cd895ebba4a541c&cdntagname=nf430&combuf=EHH1PZ1ptGGm%2F2TelPfGAiqCvbmt0I7DKGjwLFYs51s1XCiY7LPKRNDuVnz1k7QB36%2FgJDGZCvwAkH6qWXx5IF9jsdGArUh96f%2BFVEv3YhSLBRD4NgaYZIH6lhKMU9A%2FfsIfgiNHplt0lMV%2B0mauKZRkM6mNTq6vtwyDg6zwgsfvTpjUIW5IMLcMKa8sKKssvJ94SZJULjTWT2thhRKeP1tTxIBBNs%2BD1XLvJXHfSG4%2FT6dY&sc=7&wu=1&tencent_test_client_ip=*************', // 视频URL
              allViews['腾讯充值'][view.video[0]?.orientation]
            ),
            duration: 0 - videoNodeOffsetTime,
            id: uuid(),
            key,
            offset: videoNodeOffsetTime,
          };
        }
      }
    }
  };
};
const createScriptTXCZVirtualPlugin = (
  virtualManKeyOrConf: string | Record<string, any>,
  voice?: any,
  getStyle: (view: Script['views'][0]) => CommonStyle,
  getWrapperStyle?: (view: Script['views'][0]) => React.CSSProperties
) => {
  return (script: Script, timelineKey: number, key: number): SinglePlugin => {
    return (conf: PagedooPlayConfig) => {
      for (const view of script.views) {
        const index = script.views.indexOf(view);
        if (view?.virtualman?.key) {
          const voiceConfig = voice || VOICE_ID_MAP[CURRENT_LANGUAGE];
          const isMainVirtualman =
            (typeof virtualManKeyOrConf === 'string' &&
              view?.virtualman?.key === virtualManKeyOrConf) ||
            (typeof virtualManKeyOrConf === 'object' &&
              view?.virtualman?.key === virtualManKeyOrConf.key);
          const isSubVirtualman =
            (typeof virtualManKeyOrConf === 'string' &&
              view?.subVirtualman?.key === virtualManKeyOrConf) ||
            (typeof virtualManKeyOrConf === 'object' &&
              view?.subVirtualman?.key === virtualManKeyOrConf.key);
          const isInactive = !isMainVirtualman && !isSubVirtualman;
          const hidden = isFullScreenMode(view.台词文案) || isInactive;
          const component = virtualmanTXCZ(
            key,
            getStyle(view),
            virtualManKeyOrConf,
            view?.互动画面 === '是' && !isInactive ? 'comment' : 'text',
            isMainVirtualman,
            {
              platform: 'rvc',
              speed: getParam('tts_speed') || voiceConfig.speed || 1,
              style: getParam('tts_style') || voiceConfig?.style,
              voiceId: getParam('tts_id') || voiceConfig.id,
              driverMode: 'voice',
              voiceExtendConfig: JSON.stringify({
                ShortName: getParam('tts_id') || voiceConfig.id,
                Gender: voiceConfig.gender || 'Female',
                Locale: voiceConfig.locale,
              }),
            },
            getWrapperStyle?.(view) || {}
          );
          conf.timeline[timelineKey].node[index] = {
            __config: { thumbnail: '', title: '', type: 'component' },
            actualDuration: 0,
            component,
            hidden,
            duration: 0,
            id: uuid(),
            key,
            offset: 0,
          };
        }
      }
    };
  };
};

const maleVoiceKeys = [
  '769d7bda153cf58725ee17073239e615',
  'f992be51beb28c2a8eb3bb1928a86d37',
  '41017f860baf7029afcfbbc01965bcd3',
  '41397f27c22a3c28898829e5a553f005',
  '9e874c82ea4419519e61026dcd2f5b79',
  '1f53b22d587672bf30aad6638136c937',
  '4733c1803a3144efabb899ce49f21fcc',
];
const scriptTXCZVirtualManDefaultPlugin = createScriptTXCZVirtualPlugin(
  VIRTUALMAN.human_primary.config || VIRTUALMAN.human_primary.key,
  undefined,
  (view) => {
    // if (view.subVirtualman) {
    //   return isSubVirtualman ? allViews['腾讯充值'].human_right : allViews['腾讯充值'].human_left;
    // }
    return isVirtualmanInClippingRegion(view?.台词文案)
      ? allViews['腾讯充值'].human_using_phone_in_fullscreen
      : allViews['腾讯充值'].human_primary;
  }
);
// 数字人 - 玩手机（竖屏）姿势
const scriptTXCZVirtualManSecondaryPlugin = createScriptTXCZVirtualPlugin(
  VIRTUALMAN.using_phone_in_portrait_orientation.config ||
    VIRTUALMAN.using_phone_in_portrait_orientation.key,
  maleVoiceKeys.includes(VIRTUALMAN.using_phone_in_portrait_orientation.key)
    ? VOICE_ID_MAP[`${CURRENT_LANGUAGE}2`]
    : VOICE_ID_MAP[`${CURRENT_LANGUAGE}`] ||
        VOICE_ID_MAP[`${CURRENT_LANGUAGE}`],
  (view) => {
    if (view.subVirtualman) {
      return allViews['腾讯充值'].human_left;
    }
    return allViews['腾讯充值'].human_using_phone_in_portrait_orientation;
  },
  (view) => {
    if (view.subVirtualman) {
      return { clipPath: `inset(0 0.67rem 0 0)` };
    }
    return {};
  }
);
// 数字人 - 玩手机（横屏）姿势
const scriptTXCZVirtualManThirdPlugin = createScriptTXCZVirtualPlugin(
  VIRTUALMAN.using_phone_in_landscape_orientation.config ||
    VIRTUALMAN.using_phone_in_landscape_orientation.key,
  maleVoiceKeys.includes(VIRTUALMAN.using_phone_in_landscape_orientation.key)
    ? VOICE_ID_MAP[`${CURRENT_LANGUAGE}2`]
    : VOICE_ID_MAP[`${CURRENT_LANGUAGE}`] ||
        VOICE_ID_MAP[`${CURRENT_LANGUAGE}`],
  (view) => {
    if (view.subVirtualman) {
      return allViews['腾讯充值'].human_right;
    }
    return allViews['腾讯充值'].human_using_phone_in_landscape_orientation;
  },
  (view) => {
    if (view.subVirtualman) {
      return { clipPath: `inset(0 0 0 0.705rem)` };
    }
    return {};
  }
);

// 前景
const scriptTXCZForegroundPlugin = (
  script: Script,
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      // if (isFullScreenMode(view.台词文案)) {
      //   return;
      // }
      if (view.foregroundImage?.[0]?.url && !isFullScreenMode(view.台词文案)) {
        conf.timeline[timelineKey].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveImage(
            key,
            view.foregroundImage[0]?.url || '',
            view.subVirtualman
              ? allViews['腾讯充值'].foreground_double
              : allViews['腾讯充值'].foreground
          ),
          duration: 0,
          id: uuid(),
          key,
          offset: 0,
        };
      }
    }
  };
};

// 转场
const transitionDuration = 3500; // 3000ms有点长，可能画面还没出来人就开始说话。2500ms比较合适
const scriptTXCZTransitionsPlugin = (
  script: Script,
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (view?.transitionImage) {
        conf.timeline[timelineKey].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveImage(
            key,
            view?.transitionImage[0]?.url || '',
            allViews['腾讯充值'].transition
          ),
          duration: transitionDuration,
          id: uuid(),
          key,
          offset: 0,
        };
      }
    }
  };
};

// 转场预加载
const scriptTXCZTransitionsPreloadPlugin = (
  script: Script,
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (view?.transitionImage) {
        conf.timeline[timelineKey].node[index - 1] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveImage(
            key,
            view?.transitionImage[0]?.url || '',
            allViews['腾讯充值'].transition
          ),
          duration: 0 - transitionDuration,
          hidden: true,
          id: uuid(),
          key, // timeline[7]和timeline[8]的key需保持一致，确保相邻node同一张转场图片只加载1次
          offset: transitionDuration,
        };
      }
    }
  };
};

// Midasbuy 福袋广告
const scriptMidasbuyLuckyBagPlugin = (
  script: Script,
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      const affectiveSecond =
        parseInt(
          view?.luckyBagEventParameter?.mount?.affective_second?.replace(
            /\D+/g,
            ''
          ) || '',
          10
        ) || 900;
      if (view?.luckyBagEventParameter) {
        conf.timeline[timelineKey].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: midasbuyEvent(key, {
            ...view?.luckyBagEventParameter?.mount,
            affective_second: affectiveSecond,
            event_type: 'START_LUCKY_BAG_LOTTERY',
          }),
          duration: 0,
          id: uuid(),
          key,
          offset: 0,
        };
      }
    }
  };
};

// Midasbuy 贴片广告
const scriptMidasbuyPurchasePlugin = (
  script: Script,
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      const affectiveSecond =
        parseInt(
          view?.eventParameter?.mount?.affective_second?.replace(/\D+/g, '') ||
            '',
          10
        ) || 900;
      if (view?.event === 'PUSH_LIVE_POP_AD_START') {
        conf.timeline[timelineKey].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: midasbuyEvent(
            key,
            {
              ...view?.eventParameter?.mount,
              affective_second: affectiveSecond,
              event_type: 'PUSH_LIVE_POP_AD_START',
            },
            {
              ...view?.eventParameter?.unmount,
              event_type: 'PUSH_LIVE_POP_AD_STOP',
            }
          ),
          duration: 0,
          id: uuid(),
          key,
          offset: 0,
        };
      }
    }
  };
};

// 话术
const scriptTXCZSpeechPlugin = (
  script: Script,
  timelineKey: number,
  key: number
): SinglePlugin => {
  if (useTencentCloudTTS) {
    return () => {
      return;
    };
  }

  return (conf: PagedooPlayConfig) => {
    for (const view of script.views) {
      const index = script.views.indexOf(view);
      if (!isFullScreenMode(view?.台词文案)) {
        let wordText = view?.台词文案 || '';
        wordText = isFullScreenMode(wordText)
          ? ''
          : wordText.replace(CLIPPING_SHAPE_KEYWORD, '');
        conf.timeline[timelineKey].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveSpeech(
            key,
            {
              type:
                view?.互动画面 === '是' || view.video?.[0]?.type === 'live'
                  ? 'answer'
                  : 'text',
              text: wordText,
            },
            { type: 'virtualman' },
            allViews['腾讯充值'].hidden,
            view.video?.[0]?.type === 'live'
          ),
          duration: 0,
          id: uuid(),
          key,
          offset: 0,
        };
      }
    }
  };
};

const scriptTXCZduration: SinglePlugin = (conf) => {
  const max = Math.max(...conf.timeline.map((i) => i.node.length));
  let offset = 0;
  for (let i = 0; i < max; i++) {
    const LiveSpeech = conf.timeline
      .map((timeline) => timeline.node[i])
      .filter(Boolean)
      .find(
        (i) =>
          i.component.id.includes('LiveSpeech') &&
          i.component.data.speechData.text // 注意不能漏了这里，否则会导致图片预加载逻辑出错
      );
    const LiveVideo = conf.timeline
      .map((timeline) => timeline.node[i])
      .filter(Boolean)
      .find((i) => i.component.id.includes('LiveVideo'));

    // 如果视频明确声明了duration，以视频的duration为准
    const _duration =
      LiveVideo?.duration && LiveVideo.duration > 0
        ? LiveVideo.duration
        : Math.max(
            LiveSpeech
              ? LiveSpeech.component.data.speechData.text.length *
                  LANGUAGE_SPEED
              : 10000,
            1000
          );
    for (const pagedooPlayTimeline of conf.timeline) {
      const node = pagedooPlayTimeline.node[i];
      if (!node) continue;
      node.duration =
        node.duration < 0
          ? _duration + node.duration
          : node.duration > 0
          ? node.duration
          : _duration;
      node.offset += offset;
    }
    conf.fragment.push({
      __config: { name: '' },
      id: uuid(),
      offset,
    });
    offset += _duration;
  }
  // const BackgroundMusic = conf.timeline
  //   .flatMap((timeline) => timeline.node)
  //   .filter(Boolean)
  //   .filter((data) => data.component.id.includes('BackgroundMusic'))[0];
  // if (BackgroundMusic) {
  //   BackgroundMusic.duration = offset;
  // }
};

export function getTimelineIndex(init = -1) {
  const result = init + 1;
  return result;
}

export function getTimelineNodeKey(key = 2000) {
  return key + 1;
}

export const pagedooScriptPlugin = async (
  originalScript: Script,
  options: {
    templateId?: string;
    isVideo?: boolean;
    isLive?: boolean;
  } = {
    templateId: undefined,
    isLive: undefined,
    isVideo: undefined,
  }
): Promise<ScriptPlugin> => {
  const script = cloneDeep(originalScript);
  script.type = ORIGIN_TYPE.TXCZ_LIVE;
  const mockScript = {
    backgroundImage: [
      {
        url: '',
      },
    ],
    npcId: 'pagedoo_0029',
    type: ORIGIN_TYPE.TXCZ_LIVE,
    size: [455, 812],
    views: scriptData.map((i) => ({
      ...i,
      画面类型: '',
      画面名称: '',
      画面内容: '',
      时长: '',
      运镜手法: '',
      景深: '近景',
      画面商品: '',
      画面商品内容: '',
      背景图片: '',
      是否展示数字人: '是',
      互动开始: '',
      互动结束: '',
      productImage: [],
    })),
  } as Script;
  const timeline = [
    TXCZ_TEMPLATE_COMPONENTS.BKG,
    TXCZ_TEMPLATE_COMPONENTS.VIDEO_BKG,
    TXCZ_TEMPLATE_COMPONENTS.VIDEO,
    TXCZ_TEMPLATE_COMPONENTS.VIRTUALMAN_DEFAULT,
    TXCZ_TEMPLATE_COMPONENTS.VIRTUALMAN_PORTRAIT,
    TXCZ_TEMPLATE_COMPONENTS.VIRTUALMAN_LANDSCAPE,
    TXCZ_TEMPLATE_COMPONENTS.SPEECH,
    TXCZ_TEMPLATE_COMPONENTS.FOREGROUND,
    TXCZ_TEMPLATE_COMPONENTS.TRANSITION,
    TXCZ_TEMPLATE_COMPONENTS.PRE_TRANSITION,
    TXCZ_TEMPLATE_COMPONENTS.BACKGROUND_MUSIC,
    TXCZ_TEMPLATE_COMPONENTS.LUCKY_BAG,
    TXCZ_TEMPLATE_COMPONENTS.PURCHASE,
  ];

  return [
    initPlugin(mockScript, timeline.length),
    // 背景
    scriptTXCZBkgPlugin(
      mockScript,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.BKG),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.BKG))
    ),
    // 视频背景
    // scriptTXCZVideoBkgPlugin(
    //   mockScript,
    //   timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIDEO_BKG),
    //   getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIDEO_BKG))
    // ),
    // 视频组件
    await scriptTXCZVideoPlugin(
      mockScript,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIDEO),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIDEO))
    ),
    // 直播推流组件
    scriptTXCZPlayPullPlugin(
      mockScript,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIDEO),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIDEO))
    ),
    // 数字人1
    scriptTXCZVirtualManDefaultPlugin(
      mockScript,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIRTUALMAN_DEFAULT),
      getTimelineNodeKey(
        timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIRTUALMAN_DEFAULT)
      )
    ),
    // 数字人2
    scriptTXCZVirtualManSecondaryPlugin(
      mockScript,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIRTUALMAN_PORTRAIT),
      getTimelineNodeKey(
        timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIRTUALMAN_PORTRAIT)
      )
    ),
    // 数字人3
    scriptTXCZVirtualManThirdPlugin(
      mockScript,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIRTUALMAN_LANDSCAPE),
      getTimelineNodeKey(
        timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIRTUALMAN_LANDSCAPE)
      )
    ),
    // 话术组件（自研tts需要搭配话术组件使用）
    scriptTXCZSpeechPlugin(
      mockScript,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.SPEECH),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.SPEECH))
    ),
    // 前景
    scriptTXCZForegroundPlugin(
      mockScript,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.FOREGROUND),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.FOREGROUND))
    ),
    // 转场
    scriptTXCZTransitionsPlugin(
      mockScript,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.TRANSITION),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.TRANSITION))
    ),
    // 转场预加载
    scriptTXCZTransitionsPreloadPlugin(
      mockScript,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.PRE_TRANSITION),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.TRANSITION)) // 与“转场”保持key值相同，保证切换环节时不会被销毁
    ),
    scriptAdBackgroundMusicPlugin(
      mockScript,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.BACKGROUND_MUSIC),
      getTimelineNodeKey(
        timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.BACKGROUND_MUSIC)
      )
    ),
    scriptMidasbuyLuckyBagPlugin(
      mockScript,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.LUCKY_BAG),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.LUCKY_BAG))
    ),
    scriptMidasbuyPurchasePlugin(
      mockScript,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.PURCHASE),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.PURCHASE))
    ),
    useTencentCloudTTS ? duration() : scriptTXCZduration,
    livePlugin,
    format,
    setMeta({ size: mockScript.size }),
  ];
};

export const genPagedooScriptPlugin = ({
  script,
  templateId,
  isVideo = false,
  isLive = false,
}: {
  script: BaseScript;
  templateId: string;
  isVideo?: boolean;
  isLive?: boolean;
}): ScriptPlugin => {
  return pagedooScriptPlugin(script, {
    templateId,
    isLive,
    isVideo,
  });
};

export const getTemplateInfo = (): Template | null => {
  const {
    origin, // 来源，与script的type值相同
    contentType, // 脚本类型（video或live）
    templateId, // 模板ID
  } = getParams();
  let template: Template | null = null;
  // 是否是空模板
  //  const isEmptyTemplate = templateId === EMPTY_TEMPLATE_ID;
  const isVideo = contentType === CONTENT_TYPE_MAP.Video.value;
  //  const isLive = contentType === CONTENT_TYPE_MAP.Live.value;
  const availableTemplates = isVideo ? VIDEO_TEMPLATES : LIVE_TEMPLATES;
  Object.entries(availableTemplates).map(([key, item]) => {
    if (item[templateId]) {
      template = item[templateId];
    }
  });
  return template;
};
